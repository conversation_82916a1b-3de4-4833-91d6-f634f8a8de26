#!/usr/bin/env python3
"""
测试模型迁移
验证模型是否正确迁移到算法模块
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_model_definition():
    """测试模型定义"""
    print("=== 测试模型定义 ===")
    
    try:
        from service.gimbal_align import gimbal_align_model
        print(f"模型类型: {type(gimbal_align_model)}")
        print("✅ 模型导入成功！")
        return True
    except Exception as e:
        print(f"❌ 模型导入失败: {e}")
        return False


def test_algorithm_interface():
    """测试算法接口"""
    print("\n=== 测试算法接口 ===")
    
    try:
        from service.gimbal_align import process_gimbal_alignment
        
        # 检查函数签名
        import inspect
        sig = inspect.signature(process_gimbal_alignment)
        params = list(sig.parameters.keys())
        
        print(f"函数参数: {params}")
        
        # 验证参数
        expected_params = ['frame', 'frame_width', 'frame_height', 'frame_count', 'target_classes', 'distance']
        for param in expected_params:
            assert param in params, f"缺少参数: {param}"
        
        print("✅ 算法接口正确！")
        return True
    except Exception as e:
        print(f"❌ 算法接口测试失败: {e}")
        return False


def test_result_structure():
    """测试结果结构"""
    print("\n=== 测试结果结构 ===")
    
    try:
        from service.gimbal_align import GimbalAlignResult
        
        result = GimbalAlignResult()
        
        # 检查必要的属性
        required_attrs = [
            'status_msg', 'pitch_adjust', 'yaw_adjust', 'zoom_msg', 'gimbal_type_msg',
            'is_gimbal_adjust', 'is_take_photo', 'is_zoom', 'is_gimbal_type', 'detection_results'
        ]
        
        for attr in required_attrs:
            assert hasattr(result, attr), f"缺少属性: {attr}"
        
        print(f"结果对象属性: {[attr for attr in dir(result) if not attr.startswith('_')]}")
        print("✅ 结果结构正确！")
        return True
    except Exception as e:
        print(f"❌ 结果结构测试失败: {e}")
        return False


def test_constants_availability():
    """测试常量可用性"""
    print("\n=== 测试常量可用性 ===")
    
    try:
        from service.gimbal_align import (
            CONFIDENCE_THRESHOLDS, 
            CLASS_NAME_MAPPING,
            MAX_ADJUST_DEGREE,
            PITCH_STEP_NO_TARGET,
            FOCAL_LENGTH
        )
        
        print(f"CONFIDENCE_THRESHOLDS: {CONFIDENCE_THRESHOLDS}")
        print(f"CLASS_NAME_MAPPING: {CLASS_NAME_MAPPING}")
        print(f"MAX_ADJUST_DEGREE: {MAX_ADJUST_DEGREE}")
        print(f"PITCH_STEP_NO_TARGET: {PITCH_STEP_NO_TARGET}")
        print(f"FOCAL_LENGTH: {FOCAL_LENGTH}")
        
        print("✅ 所有常量可用！")
        return True
    except Exception as e:
        print(f"❌ 常量测试失败: {e}")
        return False


def test_bridge_inspection_import():
    """测试业务模块导入"""
    print("\n=== 测试业务模块导入 ===")
    
    try:
        # 只测试语法，不执行复杂逻辑
        import service.bridge_inspection
        print("✅ bridge_inspection模块导入成功！")
        return True
    except Exception as e:
        print(f"❌ bridge_inspection模块导入失败: {e}")
        return False


if __name__ == "__main__":
    print("开始测试模型迁移...")
    
    tests = [
        test_constants_availability,
        test_result_structure,
        test_algorithm_interface,
        test_model_definition,
        test_bridge_inspection_import,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试异常: {e}")
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！模型迁移成功。")
    else:
        print("❌ 部分测试失败，请检查代码。")
        sys.exit(1)

# 目标优先级管理逻辑补充说明

## 问题背景

在第一次重构后，发现缺少了原始代码中的 `last_main_target` 逻辑。这个逻辑对于无人机云台控制非常重要，主要解决以下问题：

1. **目标检测不稳定**：由于网络延迟、光照变化等因素，目标检测可能出现短暂丢失
2. **频繁切换问题**：在demo和kk目标之间频繁切换会导致云台抖动
3. **优先级管理**：需要明确的目标优先级策略

## 解决方案

### 1. 新增 TargetPriorityManager 类

```python
class TargetPriorityManager:
    """目标优先级管理器，处理目标连续性和优先级逻辑"""
    
    def __init__(self):
        self.last_main_target = None          # 上一帧的主要目标类型
        self.demo_missing_count = 0           # demo目标连续丢失的帧数
        self.max_demo_missing_frames = 2      # 允许demo目标最多丢失的帧数
```

### 2. 核心处理逻辑

#### 目标优先级规则
1. **demo优先**：当同时检测到demo和kk时，优先处理demo
2. **连续性保护**：demo短暂丢失时，保持demo优先级最多2帧
3. **平滑切换**：超过阈值后才切换到kk目标

#### 状态转换图
```
无目标 → demo目标 → demo优先
demo目标 → demo+kk目标 → demo优先（清空kk）
demo目标 → kk目标 → demo_missing（等待1-2帧）
demo_missing → demo目标 → demo优先
demo_missing → kk目标（超过2帧）→ kk优先
kk目标 → demo目标 → demo优先
```

### 3. 关键方法

#### update_target_priority()
```python
def update_target_priority(self, demo_boxes, kk_boxes):
    """
    更新目标优先级，处理目标连续性
    
    返回：
    - (demo_boxes, kk_boxes, current_main_target): 处理后的目标框和当前主目标
    """
```

**返回值说明：**
- `"demo"`: 当前帧处理demo目标
- `"kk"`: 当前帧处理kk目标  
- `"demo_missing"`: demo短暂丢失，暂停云台动作等待重新出现
- `None`: 没有任何目标

### 4. 状态管理功能

```python
# 重置状态（新任务开始时调用）
reset_gimbal_align_state()

# 获取当前状态（调试用）
state = get_gimbal_align_state()
```

## 实际效果

### 测试结果验证

运行 `test_target_priority.py` 的测试结果显示：

1. ✅ **同时检测demo和kk**：正确优先选择demo，清空kk
2. ✅ **demo短暂丢失1-2帧**：返回`demo_missing`，暂停云台动作
3. ✅ **demo丢失超过2帧**：切换到kk目标
4. ✅ **demo重新出现**：立即切换回demo优先
5. ✅ **边界情况处理**：无目标、仅kk目标等情况正常处理

### 与原始逻辑对比

**原始代码（复杂且分散）：**
```python
# 如果同时有kk和demo，只保留优先级高的目标
if demo_boxes and kk_boxes:
    logger.info("同帧检测到kk和demo，优先保留demo")
    kk_boxes = []
    last_main_target = "demo"
elif demo_boxes:
    last_main_target = "demo"
elif kk_boxes:
    # 仅在上一帧是demo且本帧短暂丢失demo时，允许延续demo优先
    if 'last_main_target' in locals() and last_main_target == "demo":
        if 'demo_missing_count' not in locals():
            demo_missing_count = 1
        else:
            demo_missing_count += 1
        if demo_missing_count <= 2:
            pass  # 保持demo优先
        else:
            last_main_target = "kk"
            demo_missing_count = 0
    else:
        last_main_target = "kk"
        demo_missing_count = 0
```

**重构后（简洁且封装）：**
```python
# 使用目标优先级管理器处理
demo_boxes, kk_boxes, current_main_target = _target_priority_manager.update_target_priority(demo_boxes, kk_boxes)

# 根据结果进行相应处理
if current_main_target == "demo_missing":
    result.status_msg = f"demo目标短暂丢失(第{_target_priority_manager.demo_missing_count}帧)，等待重新出现"
    # 暂停云台动作
```

## 业务集成

### 在 bridge_inspection.py 中的集成

1. **导入模块**：
```python
from service.gimbal_align import process_gimbal_alignment, reset_gimbal_align_state
```

2. **状态重置**：
```python
# 每次新任务开始时重置状态
reset_gimbal_align_state()
```

3. **算法调用**：
```python
# 调用算法处理云台对准（包含目标优先级管理）
align_result = process_gimbal_alignment(
    detection_results=results,
    frame_width=width,
    frame_height=height,
    frame_count=frame_count,
    target_classes=target_classes,
    distance=D,
)
```

## 优势总结

### 1. 稳定性提升
- **减少云台抖动**：避免频繁的目标切换
- **检测容错性**：允许短暂的检测失误
- **平滑过渡**：目标切换更加平滑

### 2. 代码质量
- **逻辑封装**：复杂的状态管理逻辑被封装在专门的类中
- **易于测试**：可以独立测试目标优先级逻辑
- **易于维护**：状态管理逻辑集中，便于修改和优化

### 3. 可配置性
- **可调参数**：`max_demo_missing_frames` 可以根据实际需要调整
- **状态可见**：提供状态查询功能，便于调试
- **状态可控**：提供状态重置功能

## 使用建议

1. **参数调优**：根据实际场景调整 `max_demo_missing_frames` 参数
2. **状态监控**：在调试时使用 `get_gimbal_align_state()` 监控状态
3. **日志记录**：关注 `demo_missing` 状态的日志，了解检测稳定性
4. **性能考虑**：状态管理开销很小，不会影响实时性

这次补充完善了重构后代码的功能完整性，确保了与原始代码的功能等价性，同时提供了更好的代码结构和可维护性。

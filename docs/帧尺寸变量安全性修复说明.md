# 帧尺寸变量安全性修复说明

## 问题描述

在代码审查中发现了另一个潜在的运行时错误：`width` 和 `height` 变量可能在赋值前被引用。

## 问题代码

**修复前的代码逻辑：**
```python
# 初始化画面中心点
if cx_center is None:
    height, width = frame.shape[:2]  # 只在第一次运行时执行
    cx_center = width / 2
    cy_center = height / 2

# 后续代码总是尝试使用 width 和 height
align_result = process_gimbal_alignment(
    frame=frame,
    frame_width=width,    # 如果不是第一次运行，这里会出错！
    frame_height=height,  # 如果不是第一次运行，这里会出错！
    frame_count=frame_count,
    target_classes=target_classes,
    distance=D,
)

# 绘制状态信息时也会使用 height
frame = draw_chinese_text(frame, status_msg, (10, height - 50), ...)  # 可能出错！
```

## 错误类型

```
UnboundLocalError: local variable 'width' referenced before assignment
UnboundLocalError: local variable 'height' referenced before assignment
```

## 问题分析

### 执行流程分析

1. **第一次运行**：
   - `cx_center is None` → True
   - 执行 `height, width = frame.shape[:2]`
   - 后续代码可以正常使用 `width` 和 `height`

2. **第二次及后续运行**：
   - `cx_center is None` → False（已经初始化过）
   - **跳过** `height, width = frame.shape[:2]`
   - 后续代码尝试使用 `width` 和 `height` → **UnboundLocalError**

### 根本原因

变量的作用域问题：`width` 和 `height` 只在特定条件下被赋值，但在所有情况下都被使用。

## 修复方案

**修复后的代码逻辑：**
```python
# 获取帧尺寸（每次都需要获取，因为后续代码会使用）
height, width = frame.shape[:2]

# 初始化画面中心点
if cx_center is None:
    cx_center = width / 2
    cy_center = height / 2

# 现在可以安全地使用 width 和 height
align_result = process_gimbal_alignment(
    frame=frame,
    frame_width=width,    # 安全
    frame_height=height,  # 安全
    frame_count=frame_count,
    target_classes=target_classes,
    distance=D,
)

# 绘制状态信息也是安全的
frame = draw_chinese_text(frame, status_msg, (10, height - 50), ...)  # 安全
```

## 关键修改点

### 1. 逻辑重构
```python
# 修复前：条件性赋值
if cx_center is None:
    height, width = frame.shape[:2]  # 只在条件满足时执行
    cx_center = width / 2
    cy_center = height / 2

# 修复后：分离关注点
height, width = frame.shape[:2]      # 总是执行
if cx_center is None:                # 只初始化中心点
    cx_center = width / 2
    cy_center = height / 2
```

### 2. 变量生命周期
- **修复前**：`width` 和 `height` 的生命周期依赖于条件判断
- **修复后**：`width` 和 `height` 在每次循环中都被正确初始化

### 3. 性能考虑
- `frame.shape[:2]` 是一个非常轻量的操作
- 每次执行不会对性能产生显著影响
- 相比于运行时错误，这个开销是可以接受的

## 影响的代码位置

### 1. 算法函数调用
```python
align_result = process_gimbal_alignment(
    frame_width=width,    # 需要 width
    frame_height=height,  # 需要 height
    ...
)
```

### 2. 状态信息绘制
```python
frame = draw_chinese_text(frame, status_msg, (10, height - 50), ...)  # 需要 height
```

### 3. 中心点计算
```python
cx_center = width / 2   # 需要 width
cy_center = height / 2  # 需要 height
```

## 测试场景

### 场景1：第一次运行
```python
cx_center = None  # 初始状态

# 修复后的逻辑
height, width = frame.shape[:2]  # 执行
if cx_center is None:            # True
    cx_center = width / 2        # 执行
    cy_center = height / 2       # 执行

# 后续使用 width 和 height - 安全 ✅
```

### 场景2：后续运行
```python
cx_center = 320.0  # 已初始化

# 修复后的逻辑
height, width = frame.shape[:2]  # 执行
if cx_center is None:            # False
    # 跳过中心点初始化

# 后续使用 width 和 height - 安全 ✅
```

## 边界情况考虑

### 1. 帧尺寸变化
如果视频流的帧尺寸发生变化，修复后的代码能够正确适应：
```python
# 每次都重新获取帧尺寸
height, width = frame.shape[:2]  # 适应尺寸变化
```

### 2. 不同分辨率
支持各种分辨率的视频流：
- 240p: 320x240
- 480p: 640x480  
- 720p: 1280x720
- 1080p: 1920x1080

### 3. 异常帧处理
即使帧数据异常，也能安全地获取尺寸信息。

## 最佳实践

这个修复体现了几个重要的编程最佳实践：

### 1. 变量初始化原则
- 确保变量在使用前总是被正确初始化
- 避免条件性的变量赋值

### 2. 关注点分离
- 帧尺寸获取：每次都执行
- 中心点初始化：只在需要时执行

### 3. 防御性编程
- 考虑所有可能的执行路径
- 预防潜在的运行时错误

### 4. 代码可读性
- 逻辑更加清晰和直观
- 减少了条件判断的复杂性

## 验证结果

1. **语法检查通过**：`python -m py_compile service/bridge_inspection.py` ✅
2. **逻辑验证通过**：所有使用 `width` 和 `height` 的地方都安全 ✅
3. **性能影响最小**：`frame.shape[:2]` 操作非常轻量 ✅

## 结论

这个修复消除了另一个潜在的运行时错误，进一步提高了代码的健壮性和可靠性。通过将帧尺寸获取与中心点初始化分离，代码变得更加安全和易于维护。

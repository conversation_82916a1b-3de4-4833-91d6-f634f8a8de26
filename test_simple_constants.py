#!/usr/bin/env python3
"""
简单的常量配置测试
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_gimbal_align_constants():
    """测试gimbal_align模块的常量"""
    print("=== 测试gimbal_align模块常量 ===")
    
    from service.gimbal_align import CONFIDENCE_THRESHOLDS, CLASS_NAME_MAPPING
    
    print(f"CONFIDENCE_THRESHOLDS: {CONFIDENCE_THRESHOLDS}")
    print(f"CLASS_NAME_MAPPING: {CLASS_NAME_MAPPING}")
    
    # 验证置信度阈值
    assert CONFIDENCE_THRESHOLDS['demo'] == 0.1
    assert CONFIDENCE_THRESHOLDS['kk'] == 0.1
    
    # 验证类别映射
    assert CLASS_NAME_MAPPING['demo'] == ('桥墩', (0, 155, 0))
    assert CLASS_NAME_MAPPING['kk'] == ('不完整桥墩', (0, 0, 155))
    
    print("✅ gimbal_align模块常量正确！")


def test_bridge_inspection_import():
    """测试bridge_inspection模块的导入"""
    print("\n=== 测试bridge_inspection模块导入 ===")
    
    # 只测试导入，不执行复杂逻辑
    try:
        import service.bridge_inspection
        print("✅ bridge_inspection模块导入成功！")
    except Exception as e:
        print(f"❌ bridge_inspection模块导入失败: {e}")
        raise


if __name__ == "__main__":
    try:
        test_gimbal_align_constants()
        test_bridge_inspection_import()
        print("\n🎉 所有常量配置测试通过！")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

import multiprocessing as mp
from flask import Flask

from websocket_client import WebSocketClient

if mp.get_start_method(allow_none=True) is None:
    mp.set_start_method('spawn')

app = Flask(__name__)

# 只在主进程中创建websocket连接
if mp.current_process().name == 'MainProcess':
    ws_client = WebSocketClient()
    ws_client.connect()

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)

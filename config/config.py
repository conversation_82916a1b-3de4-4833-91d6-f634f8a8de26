import os

import loguru
import yaml

# 获取当前脚本的绝对路径
script_path = os.path.abspath(__file__)
# 获取脚本所在目录
script_dir = os.path.dirname(script_path)


def init_log():
    # 初始logger
    global logger
    # 配置日志输出格式和级别
    # loguru.logger.remove(0)
    loguru.logger.add("app.log", rotation="500 MB", level="INFO", format="{time} {level} {message}")
    logger = loguru.logger


def init_config():
    global config_data
    # 读取配置文件
    config_path = os.path.join(script_dir, 'config.yml')
    with open(config_path, 'r') as file:
        config_data = yaml.safe_load(file)


logger = None
config_data = None

init_log()
init_config()

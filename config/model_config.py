# 福州
# target_classes = ["digger", "net", "tower-crane", "dump-truck", "piling-machine", "lifting-equipment", "concrete-pump", "engineerVeh<PERSON>"]

pt_version = '2-18-2'

confidence_thresholds = {
    'Car': 0.55,
    'digger': 0.45,
    'engineerVehicles': 0.6,
    'house': 0.4,
    # 'float': 0.55,
    # 'gaolou': 0.65,  # 自定义阈值
    # 'fire': 0.7,
    'net': 0.9,
    'rubbish': 0.6,
    # 'smoke': 0.8,
    # 'tower': 0.6,  # 自定义阈值
    'tower-crane': 0.82,
    'dump-truck': 0.56,
    'concrete-mixer': 0.4,
    'bulldozer': 0.5,
    'concrete-pump': 0.5,
    'forklift': 0.6,
    'lifting-equipment': 0.7,
    'piling-machine': 0.35,
    'roller': 0.65,
    # 'people': 0.7,
    'motorcycle': 0.65,  # 自定义阈值
    'truck': 0.65,  # 自定义阈值
    'bus': 0.45,
    # 'train': 0.85,  # 自定义阈值
    'bicycle': 0.65,  # 自定义阈值
    'tricycle': 0.65  # 自定义阈值
}

class_name_mapping = {
    'Car': ('轿车', (0, 0, 155)),
    'digger': ('挖掘机', (255, 0, 0)),
    'engineer<PERSON>ehicles': ('平板车', (0, 255, 0)),
    'house': ('板房', (0, 0, 255)),
    # 'float': ('漂浮物', (0, 255, 255)),
    # 'gaolou': ('高楼', (128, 0, 128)),  # 自定义颜色
    # 'fire': ('火灾', (255, 255, 0)),
    'net': ('绿色苫网', (161, 239, 155)),
    'rubbish': ('垃圾', (0, 128, 0)),
    # 'smoke': ('烟雾', (105, 105, 105)),  # 深灰色
    # 'tower': ('塔', (64, 64, 64)),  # 自定义颜色
    'tower-crane': ('塔吊', (0, 255, 127)),
    'dump-truck': ('自卸车', (255, 20, 147)),
    'concrete-mixer': ('混凝土搅拌车', (75, 0, 130)),
    'bulldozer': ('推土机', (0, 255, 255)),
    'concrete-pump': ('混凝土泵车', (255, 140, 0)),
    'forklift': ('叉车', (0, 0, 255)),
    'lifting-equipment': ('吊装设备', (0, 255, 0)),
    'piling-machine': ('打桩机', (255, 0, 0)),
    'roller': ('压路机', (255, 215, 0)),
    # 'people': ('人', (255, 215, 0)),
    'motorcycle': ('摩托车', (75, 0, 130)),
    'truck': ('卡车', (0, 0, 128)),
    'bus': ('公共汽车', (255, 215, 0)),
    # 'train': ('火车', (128, 128, 0)),
    'bicycle': ('自行车', (0, 128, 255)),
    'tricycle': ('三轮车', (255, 165, 0))
}

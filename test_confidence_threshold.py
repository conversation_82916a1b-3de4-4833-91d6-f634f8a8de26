#!/usr/bin/env python3
"""
测试置信度阈值逻辑
验证 CONFIDENCE_THRESHOLDS 相关功能是否正常工作
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from service.gimbal_align import CONFIDENCE_THRESHOLDS


def test_confidence_thresholds():
    """测试置信度阈值配置"""
    print("=== 测试置信度阈值配置 ===")

    print(f"CONFIDENCE_THRESHOLDS: {CONFIDENCE_THRESHOLDS}")

    # 验证配置存在
    assert 'demo' in CONFIDENCE_THRESHOLDS, "demo阈值应该存在"
    assert 'kk' in CONFIDENCE_THRESHOLDS, "kk阈值应该存在"
    assert CONFIDENCE_THRESHOLDS['demo'] == 0.1, "demo阈值应该是0.1"
    assert CONFIDENCE_THRESHOLDS['kk'] == 0.1, "kk阈值应该是0.1"

    print("✅ 置信度阈值配置正确！")


def test_threshold_logic():
    """测试阈值获取逻辑"""
    print("\n=== 测试阈值获取逻辑 ===")

    # 测试已知类别
    demo_threshold = CONFIDENCE_THRESHOLDS.get('demo', 0.1)
    kk_threshold = CONFIDENCE_THRESHOLDS.get('kk', 0.1)

    print(f"demo阈值: {demo_threshold}")
    print(f"kk阈值: {kk_threshold}")

    assert demo_threshold == 0.1, "demo阈值应该是0.1"
    assert kk_threshold == 0.1, "kk阈值应该是0.1"

    # 测试未知类别（使用默认值）
    unknown_threshold = CONFIDENCE_THRESHOLDS.get('unknown', 0.1)
    print(f"未知类别阈值: {unknown_threshold}")
    assert unknown_threshold == 0.1, "未知类别应该使用默认阈值0.1"

    print("✅ 阈值获取逻辑正确！")





if __name__ == "__main__":
    try:
        test_confidence_thresholds()
        test_threshold_logic()
        print("\n🎉 所有置信度阈值测试通过！逻辑与原始代码一致。")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

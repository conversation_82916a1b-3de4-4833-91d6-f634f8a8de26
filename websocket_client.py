import json
import threading
import time
import websocket
from config.config import logger, config_data
from service.service import handle_start_no_real_pic_time, handle_start_real_time, handle_end_real_time, \
    handle_start_no_real_video_time, start_real_time_control_req, end_real_time_control_req, real_time_control_req


def async_handle(self, message):
    data = json.loads(message)
    handler = {
        "start_real_time_control_req": lambda d: start_real_time_control_req(self, d),
        "end_real_time_control_req": lambda d: end_real_time_control_req(self, d),
        "real_time_control_req": lambda d: real_time_control_req(self, d),
    }.get(data.get("type"))

    if handler:
        handler(data.get("data"))


class WebSocketClient:
    def __init__(self):
        self.ws = None
        self.ws_connected = False

    def on_message(self, ws, message):
        logger.info(f"websocket received message: {message}")
        # 启动异步处理线程
        threading.Thread(target=async_handle, args=(self, message), daemon=True).start()

    def on_error(self, ws, error):
        logger.error(f"websocket error: {error}")
        self.ws_connected = False

    def on_close(self, ws, close_status_code, close_msg):
        logger.info("websocket connect closed")
        self.ws_connected = False
        time.sleep(5)
        self.connect()

    def on_open(self, ws):
        logger.info("websocket connect successful")
        self.ws_connected = True

    def connect(self):
        url = config_data.get("server", {}).get("websocket_connect_url")
        server_type = config_data.get("server", {}).get("type")

        try:
            websocket.enableTrace(True)
            self.ws = websocket.WebSocketApp(
                f"{url}?id={server_type}",
                on_message=self.on_message,
                on_error=self.on_error,
                on_close=self.on_close,
                on_open=self.on_open
            )
            threading.Thread(target=self.ws.run_forever, daemon=True).start()
        except Exception as e:
            logger.error(f"websocket connect error: {e}")
            self.ws_connected = False

    def send_message(self, message):
        if self.ws and self.ws_connected:
            try:
                self.ws.send(json.dumps(message))
                return True
            except Exception as e:
                logger.error(f"websocket send message error: {e}")
        return False

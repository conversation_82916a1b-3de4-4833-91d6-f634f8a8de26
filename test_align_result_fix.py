#!/usr/bin/env python3
"""
测试 align_result 变量引用修复
验证在所有情况下 align_result 都能正确初始化
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_gimbal_align_result_creation():
    """测试 GimbalAlignResult 对象创建"""
    print("=== 测试 GimbalAlignResult 对象创建 ===")
    
    try:
        from service.gimbal_align import GimbalAlignResult
        
        # 创建默认结果对象
        result = GimbalAlignResult()
        
        # 验证所有属性都有默认值
        assert result.status_msg == "", "status_msg 应该是空字符串"
        assert result.pitch_adjust == 0.0, "pitch_adjust 应该是 0.0"
        assert result.yaw_adjust == 0.0, "yaw_adjust 应该是 0.0"
        assert result.zoom_msg == "", "zoom_msg 应该是空字符串"
        assert result.gimbal_type_msg == "", "gimbal_type_msg 应该是空字符串"
        assert result.is_gimbal_adjust == False, "is_gimbal_adjust 应该是 False"
        assert result.is_take_photo == False, "is_take_photo 应该是 False"
        assert result.is_zoom == False, "is_zoom 应该是 False"
        assert result.is_gimbal_type == False, "is_gimbal_type 应该是 False"
        assert result.detection_results is None, "detection_results 应该是 None"
        
        print("✅ GimbalAlignResult 对象创建正确！")
        return True
    except Exception as e:
        print(f"❌ GimbalAlignResult 对象创建失败: {e}")
        return False


def test_disabled_gimbal_scenario():
    """测试云台控制禁用场景"""
    print("\n=== 测试云台控制禁用场景 ===")
    
    try:
        from service.gimbal_align import GimbalAlignResult
        
        # 模拟云台控制禁用时的逻辑
        gimbal_control_enabled = False
        
        if not gimbal_control_enabled:
            # 创建一个空的算法结果，表示云台控制被禁用
            align_result = GimbalAlignResult()
            align_result.status_msg = "云台控制已禁用"
        
        # 验证可以安全访问所有属性
        status_msg = align_result.status_msg
        pitch_adjust = align_result.pitch_adjust
        yaw_adjust = align_result.yaw_adjust
        zoom_msg = align_result.zoom_msg
        gimbal_type_msg = align_result.gimbal_type_msg
        is_gimbal_adjust = align_result.is_gimbal_adjust
        is_take_photo = align_result.is_take_photo
        is_zoom = align_result.is_zoom
        is_gimbal_type = align_result.is_gimbal_type
        
        print(f"状态消息: {status_msg}")
        print(f"云台调整: pitch={pitch_adjust}, yaw={yaw_adjust}")
        print(f"控制标志: gimbal={is_gimbal_adjust}, photo={is_take_photo}, zoom={is_zoom}, type={is_gimbal_type}")
        
        # 验证检测结果访问
        if align_result.detection_results:
            print("有检测结果")
        else:
            print("无检测结果（正常，因为云台控制被禁用）")
        
        print("✅ 云台控制禁用场景测试通过！")
        return True
    except Exception as e:
        print(f"❌ 云台控制禁用场景测试失败: {e}")
        return False


def test_enabled_gimbal_scenario():
    """测试云台控制启用场景（模拟）"""
    print("\n=== 测试云台控制启用场景（模拟）===")
    
    try:
        from service.gimbal_align import GimbalAlignResult
        
        # 模拟云台控制启用时的逻辑
        gimbal_control_enabled = True
        
        if gimbal_control_enabled:
            # 模拟算法处理结果
            align_result = GimbalAlignResult()
            align_result.status_msg = "检测到目标，执行云台调整"
            align_result.pitch_adjust = 5.0
            align_result.yaw_adjust = -3.0
            align_result.is_gimbal_adjust = True
            align_result.gimbal_type_msg = "变焦"
            align_result.is_gimbal_type = True
        
        # 验证可以安全访问所有属性
        status_msg = align_result.status_msg
        pitch_adjust = align_result.pitch_adjust
        yaw_adjust = align_result.yaw_adjust
        is_gimbal_adjust = align_result.is_gimbal_adjust
        
        print(f"状态消息: {status_msg}")
        print(f"云台调整: pitch={pitch_adjust}, yaw={yaw_adjust}")
        print(f"需要云台调整: {is_gimbal_adjust}")
        
        assert status_msg != "", "应该有状态消息"
        assert pitch_adjust != 0 or yaw_adjust != 0, "应该有云台调整"
        
        print("✅ 云台控制启用场景测试通过！")
        return True
    except Exception as e:
        print(f"❌ 云台控制启用场景测试失败: {e}")
        return False


def test_import_consistency():
    """测试导入一致性"""
    print("\n=== 测试导入一致性 ===")
    
    try:
        # 测试从 bridge_inspection 导入
        from service.bridge_inspection import GimbalAlignResult as BridgeGimbalAlignResult
        
        # 测试从 gimbal_align 导入
        from service.gimbal_align import GimbalAlignResult as AlignGimbalAlignResult
        
        # 验证是同一个类
        assert BridgeGimbalAlignResult is AlignGimbalAlignResult, "应该是同一个类"
        
        print("✅ 导入一致性测试通过！")
        return True
    except Exception as e:
        print(f"❌ 导入一致性测试失败: {e}")
        return False


if __name__ == "__main__":
    print("开始测试 align_result 变量引用修复...")
    
    tests = [
        test_gimbal_align_result_creation,
        test_disabled_gimbal_scenario,
        test_enabled_gimbal_scenario,
        test_import_consistency,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试异常: {e}")
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！align_result 变量引用问题已修复。")
    else:
        print("❌ 部分测试失败，请检查代码。")
        sys.exit(1)

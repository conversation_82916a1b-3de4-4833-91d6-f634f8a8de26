import multiprocessing as mp
import os
import subprocess
import time

import cv2

from config.config import logger
from utils import draw_chinese_text, make_msg_resp, draw_fps_time, validate_frame_integrity
from operator_lib.gimbal_align import process_gimbal_alignment, reset_gimbal_align_state, CONFIDENCE_THRESHOLDS, \
    CLASS_NAME_MAPPING, GimbalAlignResult

# ===== 全局变量 =====
_drone_control_exit_event = None

# 多进程共享变量
gimbal_control_enabled = None  # 云台控制逻辑开关


# ===== 全局变量更新函数 =====
def create_drone_control_exit_event():
    """创建新的drone_control_exit_event"""
    global _drone_control_exit_event
    _drone_control_exit_event = mp.Event()


def get_drone_control_exit_event():
    """获取当前的drone_control_exit_event"""
    return _drone_control_exit_event


def update_gimbal_control(state: bool):
    global gimbal_control_enabled
    if gimbal_control_enabled is not None:
        gimbal_control_enabled.value = state
        logger.info(f"[后端控制] 云台控制状态已更新：{'开启' if state else '关闭'}")
    else:
        logger.warning("gimbal_control_enabled 共享变量未初始化")


def draw_frame_status(frame, status_msg, fps_display, height):
    # 显示状态信息
    frame = draw_chinese_text(frame, status_msg, (10, height - 50), font_size=20, color=(0, 255, 255))
    # 显示FPS
    cv2.putText(frame, f"FPS: {fps_display:.2f}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
    return frame


def init_control_worker(exit_evt, shared_vars=None):
    """初始化控制工作进程
    Args:
        exit_evt: 退出事件
        shared_vars: 共享变量字典
    """
    global _drone_control_exit_event, gimbal_control_enabled

    if shared_vars:
        gimbal_control_enabled = shared_vars['gimbal_control_enabled']


def run_control_capture_video(exit_evt, shared_vars, *args):
    """包装函数，先初始化再运行capture_video"""
    init_control_worker(exit_evt, shared_vars)
    control_capture_video(*args)


def run_control_process_frames(exit_evt, shared_vars, *args):
    """包装函数，先初始化再运行process_frames"""
    init_control_worker(exit_evt, shared_vars)
    control_process_frames(*args)


def run_control_push_to_ffmpeg(exit_evt, shared_vars, *args):
    """包装函数，先初始化再运行push_to_ffmpeg"""
    init_control_worker(exit_evt, shared_vars)
    control_push_to_ffmpeg(*args)


def handle_control_error(err, err_queue):
    """处理控制错误"""
    # 将错误放入队列
    err_queue.put(err)
    # 设置退出事件
    _drone_control_exit_event.set()


def control_capture_video(pull_stream_url, frame_queue, err_queue):
    """控制模式的视频捕获进程"""
    try:
        cap = cv2.VideoCapture(pull_stream_url)
        if cap.isOpened():
            # 设置缓冲区大小为1，减少延迟
            cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            # 设置超时，避免卡死
            cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 5000)
            cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)
        else:
            cap = None
    except Exception as e:
        logger.error(f"创建视频捕获对象时发生异常: {str(e)}")
        cap = None
    if cap is None:
        err = f"failed to open source video: {pull_stream_url}"
        logger.error(err)
        handle_control_error(err, err_queue)
        return

    # 配置参数
    config = {
        'max_failures': 30,  # 最大连续失败次数阈值
        'max_corrupted_frames': 10,  # 最大连续损坏帧数阈值
        'retry_interval': 0.1  # 重试间隔时间
    }

    # 计数器
    counters = {
        'consecutive_failures': 0,  # 连续失败次数
        'corrupted_frame_count': 0  # 连续损坏帧计数
    }

    while not _drone_control_exit_event.is_set():
        try:
            # 尝试抓取帧
            if not cap.grab():
                # 处理网络失败
                counters['consecutive_failures'] += 1
                if counters['consecutive_failures'] >= config['max_failures']:
                    err = f"control_capture_video: 连续抓取失败过多，需要重启整个流处理: {pull_stream_url}"
                    logger.error(err)
                    handle_control_error(err, err_queue)
                    break
                time.sleep(config['retry_interval'])
                continue

            # 解码帧
            ret, frame = cap.retrieve()
            if not (ret and frame is not None and frame.size > 0):
                # 处理网络失败
                counters['consecutive_failures'] += 1
                if counters['consecutive_failures'] >= config['max_failures']:
                    err = f"control_capture_video: 连续解码失败过多，需要重启整个流处理: {pull_stream_url}"
                    logger.error(err)
                    handle_control_error(err, err_queue)
                    break
                time.sleep(config['retry_interval'])
                continue

            # 验证帧完整性
            start_time = time.time()
            if validate_frame_integrity(frame):
                logger.debug(f"帧验证耗时: {(time.time() - start_time) * 1000:.2f}ms")
                # 帧验证通过，重置计数器
                counters['consecutive_failures'] = 0
                counters['corrupted_frame_count'] = 0
                # 检查队列大小，如果超过阈值则丢弃帧
                if frame_queue.full():
                    try:
                        while not frame_queue.empty():
                            frame_queue.get_nowait()
                    except mp.queues.Empty:
                        pass
                frame_queue.put(frame)
            else:
                # 处理损坏帧
                counters['corrupted_frame_count'] += 1
                # 只在特定间隔输出警告，避免日志过多
                if counters['corrupted_frame_count'] % 5 == 1:  # 每5个损坏帧输出一次
                    logger.warning(f"control_capture_video: 检测到损坏帧，当前计数: {counters['corrupted_frame_count']}")

                if counters['corrupted_frame_count'] >= config['max_corrupted_frames']:
                    err = f"control_capture_video: 损坏帧过多({counters['corrupted_frame_count']})，需要重启整个流处理: {pull_stream_url}"
                    logger.error(err)
                    handle_control_error(err, err_queue)
                    break
                time.sleep(config['retry_interval'] / 2)

        except Exception as e:
            logger.error(f"control_capture_video异常: {str(e)}")
            # 处理异常导致的网络失败
            counters['consecutive_failures'] += 1
            if counters['consecutive_failures'] >= config['max_failures']:
                err = f"control_capture_video: 异常过多，需要重启整个流处理: {str(e)}"
                handle_control_error(err, err_queue)
                break
            time.sleep(config['retry_interval'])

    # 清理资源
    try:
        cap.release()
    except Exception as e:
        logger.warning(f"释放视频捕获对象时发生异常: {str(e)}")

    frame_queue.put(None)  # 发送结束信号
    logger.info("control_capture_video end")


def control_process_frames(frame_queue, processed_frame_queue, message_queue, target_classes, job_id, drone_sn, dock_sn,
                           service_type):
    """控制模式的帧处理进程"""
    frame_count = 0
    start_time = time.time()
    last_log_time = start_time
    frames_processed = 0
    fps = 0.0

    while not _drone_control_exit_event.is_set():
        try:
            # 检查处理队列大小，如果超过阈值则丢弃一些帧
            if processed_frame_queue.full():
                try:
                    while not processed_frame_queue.empty():
                        processed_frame_queue.get_nowait()
                except mp.queues.Empty:
                    pass

            # 获取单帧进行处理（不使用批处理）
            try:
                frame = frame_queue.get(timeout=0.01)
                if frame is None:
                    break
            except mp.queues.Empty:
                continue

            frame_count += 1
            frames_processed += 1
            current_time = time.time()

            # 每秒更新一次FPS显示
            if current_time - last_log_time >= 1.0:
                fps = frames_processed / (current_time - last_log_time)
                frames_processed = 0
                last_log_time = current_time

            # 获取帧尺寸（每次都需要获取，因为后续代码会使用）
            height, width = frame.shape[:2]

            # 检查云台控制是否启用
            if not (gimbal_control_enabled and gimbal_control_enabled.value):
                logger.debug(
                    f"当前后端禁止云台控制，跳过本帧所有操作。gimbal_control_enabled={gimbal_control_enabled.value if gimbal_control_enabled else None}")

                # 创建一个空的算法结果，表示云台控制被禁用
                align_result = GimbalAlignResult()
                align_result.status_msg = "云台控制已禁用"
                # 其他属性保持默认值（都是0或False）
            else:
                # 使用算法模块处理云台对准（包含目标检测）
                align_result = process_gimbal_alignment(
                    frame=frame,
                    frame_width=width,
                    frame_height=height,
                    frame_count=frame_count,
                    target_classes=target_classes,
                )

            # 从算法结果中获取业务需要的参数
            status_msg = align_result.status_msg
            pitch_adjust = align_result.pitch_adjust
            yaw_adjust = align_result.yaw_adjust
            zoom_msg = align_result.zoom_msg
            gimbal_type_msg = align_result.gimbal_type_msg
            is_gimbal_adjust = align_result.is_gimbal_adjust
            isTakePhoto = align_result.is_take_photo
            is_zoom = align_result.is_zoom
            is_gimbal_type = align_result.is_gimbal_type

            # 业务信息处理返回
            control_resp_data = {
                "job_id": job_id,
                "drone_sn": drone_sn,
                "dock_sn": dock_sn,
                "action_type": 0,
            }

            if isTakePhoto:
                # 拍照
                control_resp_data["action_type"] = 3
                logger.info("执行拍照指令")

            elif is_gimbal_adjust or is_zoom or is_gimbal_type:
                # 云台调整
                control_resp_data["action_type"] = 2

                zoom_type = 0
                if is_zoom:
                    # 焦距调整
                    if zoom_msg == "拉远焦距":
                        zoom_type = 2
                    elif zoom_msg == "拉近焦距":
                        zoom_type = 1

                gimbal_type = 0
                if is_gimbal_type:
                    # 云台模式切换
                    if gimbal_type_msg == "变焦":
                        gimbal_type = 1
                    elif gimbal_type_msg == "广角":
                        gimbal_type = 2

                control_resp_data["ai_gimbal_control"] = {
                    "pitch_adjust": pitch_adjust,
                    "yaw_adjust": yaw_adjust,
                    "zoom_type": zoom_type,
                    "gimbal_type": gimbal_type,
                }
                logger.info(
                    f"云台调整：pitch_adjust={pitch_adjust}, yaw_adjust={yaw_adjust}, zoom_type={zoom_type}, "
                    f"gimbal_type_msg={gimbal_type_msg}")

            # 添加控制状态标识，让主进程知道需要关闭云台控制
            message_data = {
                'service_type': service_type,
                'resp_data': control_resp_data,
                'disable_gimbal': True  # 标识需要关闭云台控制
            }
            logger.info(f"message_queue put:{control_resp_data}")
            message_queue.put(message_data)
            # 在子进程中立即设置云台控制为关闭状态，避免竞态问题
            if gimbal_control_enabled:
                gimbal_control_enabled.value = False
                logger.info("子进程已关闭云台控制，避免竞态问题")

            # 绘制检测框
            if align_result.detection_results and len(align_result.detection_results[0].boxes) > 0:
                for box in align_result.detection_results[0].boxes:
                    cls_id = int(box.cls[0])
                    cls_name = align_result.detection_results[0].names[cls_id]
                    confidence = box.conf[0]
                    threshold = CONFIDENCE_THRESHOLDS.get(cls_name, 0.35)

                    if confidence > threshold:
                        chinese_name, color = CLASS_NAME_MAPPING.get(cls_name, (cls_name, (0, 255, 0)))
                        xyxy = box.xyxy[0].cpu().numpy().astype(int)

                        cv2.rectangle(frame, (xyxy[0], xyxy[1]), (xyxy[2], xyxy[3]), color, 3)
                        text = f"{chinese_name} {confidence:.2f}"
                        frame = draw_chinese_text(frame, text, (xyxy[0], xyxy[1] - 20),
                                                  font_size=20, color=color)

            if status_msg:
                logger.info(status_msg)
                frame = draw_chinese_text(frame, status_msg, (10, height - 50),
                                          font_size=20, color=(0, 255, 255))

            # 绘制FPS和时间信息
            current_time_str = time.strftime("%H:%M:%S", time.localtime())
            frame = draw_fps_time(frame, fps, current_time_str, frame_count)

            # 将处理后的帧放入队列
            processed_frame_queue.put(frame)

        except Exception as e:
            logger.error(f"控制处理帧时发生错误: {str(e)}")
            continue

    processed_frame_queue.put(None)  # Signal that processing has ended
    logger.info("control_process_frames end")


def control_push_to_ffmpeg(pull_stream_url, rtmp_output, processed_frame_queue, err_queue):
    """控制模式的FFmpeg推流进程"""
    cap_temp = cv2.VideoCapture(pull_stream_url)
    width = int(cap_temp.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap_temp.get(cv2.CAP_PROP_FRAME_HEIGHT))
    cap_temp.release()

    command_push = [
        'ffmpeg',
        '-y',  # 覆盖输出文件
        '-re',  # 以本地帧率读取输入
        '-fflags', 'nobuffer',  # 禁用输入缓冲
        "-hwaccel", "cuda",  # 硬件加速
        "-hwaccel_output_format", "yuv420p",

        # 输入相关参数
        '-f', 'rawvideo',
        '-vcodec', 'rawvideo',
        '-pix_fmt', 'bgr24',
        '-s', f'{width}x{height}',
        '-i', '-',

        # 编码相关参数
        '-c:v', 'h264_nvenc',  # 使用cuda硬件编码
        '-profile:v', 'main',
        "-pix_fmt", "yuv420p",  # 输出像素格式 (兼容性最佳)

        # 输出格式
        '-f', 'flv',
        rtmp_output
    ]

    process = subprocess.Popen(command_push, stdin=subprocess.PIPE, stdout=subprocess.DEVNULL,
                               stderr=subprocess.DEVNULL, bufsize=0)

    while not _drone_control_exit_event.is_set():
        try:
            frame = processed_frame_queue.get(timeout=0.01)
        except mp.queues.Empty:
            continue

        if frame is None:
            break

        try:
            process.stdin.write(frame.tobytes())
            process.stdin.flush()
        except Exception as e:
            err = f"control_push_to_ffmpeg err: {e}"
            logger.error(err)
            handle_control_error(err, err_queue)
            break

    logger.info("control_push_to_ffmpeg end")
    process.stdin.close()
    process.wait()


def drone_control_predict(ws_client, data, service_type):
    """
    直播流检测主函数，使用三进程架构进行控制决策 - 支持自动重启。
    """
    # 无人机云台直播拉流地址
    pull_stream_url = data.get("pull_stream_url")
    # 处理后的推流地址
    push_stream_url = data.get("push_stream_url")
    # 任务id
    job_id = data.get("job_id")
    # 检测目标
    # target_classes = data.get("target_classes")
    target_classes = ["demo", "kk"]
    # 业务透传
    drone_sn = data.get("drone_sn")
    dock_sn = data.get("dock_sn")

    # 创建多进程共享变量（在重启循环外创建，避免重复创建）
    global gimbal_control_enabled
    gimbal_control_enabled = mp.Value('b', False)  # bool类型

    shared_vars = {
        'gimbal_control_enabled': gimbal_control_enabled,
    }

    max_restart_attempts = 3  # 最大重启次数
    restart_count = 0
    restart_delay = 2  # 重启延迟（秒）

    while restart_count <= max_restart_attempts:
        try:
            # 创建新的退出事件和队列
            create_drone_control_exit_event()

            # 重置云台对准算法状态
            reset_gimbal_align_state()

            frame_queue = mp.Queue(maxsize=5)
            processed_frame_queue = mp.Queue(maxsize=5)
            err_queue = mp.Queue()
            message_queue = mp.Queue()  # 新增消息队列，用于子进程向主进程发送消息

            processes = [
                mp.Process(name="control_capture_process",
                           target=run_control_capture_video,
                           args=(_drone_control_exit_event, shared_vars, pull_stream_url, frame_queue, err_queue)),
                mp.Process(name="control_process_frames_process",
                           target=run_control_process_frames,
                           args=(
                               _drone_control_exit_event, shared_vars, frame_queue, processed_frame_queue,
                               message_queue,
                               target_classes, job_id, drone_sn, dock_sn, service_type)),
                mp.Process(name="control_push_process",
                           target=run_control_push_to_ffmpeg,
                           args=(
                               _drone_control_exit_event, shared_vars, pull_stream_url, push_stream_url,
                               processed_frame_queue,
                               err_queue))
            ]

            # 启动所有进程
            for p in processes:
                p.start()

            logger.info(f"无人机控制流处理进程启动完成 (尝试次数: {restart_count + 1})")

            # 监控进程状态
            while any(p.is_alive() for p in processes):
                try:
                    # 检查错误队列
                    err = err_queue.get_nowait()
                    logger.error(f"drone_control_predict err_queue: {err}")

                    # 清理当前进程
                    _drone_control_exit_event.set()
                    for p in processes:
                        p.join(timeout=5)
                        if p.is_alive():
                            p.terminate()

                    # 判断是否需要重启
                    if restart_count < max_restart_attempts:
                        restart_count += 1
                        logger.warning(f"无人机控制流处理出错，{restart_delay}秒后重启 (第{restart_count}次重启)")
                        time.sleep(restart_delay)
                        break  # 跳出监控循环，进入重启流程
                    else:
                        logger.error(f"达到最大重启次数({max_restart_attempts})，停止无人机控制流处理")
                        return f"无人机控制流处理失败，已重启{restart_count}次: {err}"

                except mp.queues.Empty:
                    # 检查消息队列
                    try:
                        message_data = message_queue.get_nowait()

                        # 统一使用字典格式消息
                        service_type_msg = message_data['service_type']
                        resp_data = message_data['resp_data']
                        disable_gimbal = message_data.get('disable_gimbal', False)

                        # 使用主进程中的ws_client发送消息
                        ws_client.send_message(make_msg_resp(service_type_msg, data=resp_data))
                        logger.info(f"drone_control_predict send_message: {resp_data}")

                        # 如果需要，关闭云台控制（作为备份机制）
                        if disable_gimbal:
                            update_gimbal_control(False)
                            logger.info("主进程确认关闭云台控制")

                    except mp.queues.Empty:
                        pass

                    time.sleep(0.1)  # 短暂休眠避免CPU过度使用
            else:
                # 所有进程正常结束
                logger.info("无人机控制流处理正常结束")
                return None

        except Exception as e:
            logger.error(f"drone_control_predict异常: {str(e)}")
            if restart_count < max_restart_attempts:
                restart_count += 1
                logger.warning(f"无人机控制流处理异常，{restart_delay}秒后重启 (第{restart_count}次重启)")
                time.sleep(restart_delay)
            else:
                logger.error(f"达到最大重启次数({max_restart_attempts})，停止无人机控制流处理")
                return f"无人机控制流处理异常失败: {str(e)}"

    # 达到最大重启次数后的返回
    logger.error(f"无人机控制流处理达到最大重启次数({max_restart_attempts})，停止处理")
    return f"无人机控制流处理失败，已重启{restart_count}次"

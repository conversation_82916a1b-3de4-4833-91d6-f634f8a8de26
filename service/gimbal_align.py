"""
云台对准算法模块
包含所有云台控制相关的算法逻辑，与业务逻辑分离
"""

import os
from ultralytics import YOLO

# 算法相关常量
MAX_ADJUST_DEGREE = 15.0  # 云台每次最大调整角度（度）
PITCH_STEP_NO_TARGET = 3.0  # 无目标时云台每次向上调整的角度步长，单位：度
FOCAL_LENGTH = 50.0  # 相机焦距，单位：毫米，用于图像中尺寸估算或标注尺寸推断

# 置信度阈值配置
CONFIDENCE_THRESHOLDS = {
    'demo': 0.1,
    'kk': 0.1
}

# 类别名称映射配置
CLASS_NAME_MAPPING = {
    'demo': ('桥墩', (0, 155, 0)),
    'kk': ('不完整桥墩', (0, 0, 155))
}

# 模型定义
gimbal_align_model = YOLO(os.path.join('static', 'pt', '0626.pt'))

# 全局状态变量，用于跨帧状态管理
_last_main_target = None  # 上一帧的主要目标类型
_demo_missing_count = 0  # demo目标连续丢失的帧数


class GimbalAlignResult:
    """云台对准算法结果"""

    def __init__(self):
        self.status_msg = ""
        self.pitch_adjust = 0.0
        self.yaw_adjust = 0.0
        self.zoom_msg = ""
        self.gimbal_type_msg = ""
        self.is_gimbal_adjust = False
        self.is_take_photo = False
        self.is_zoom = False
        self.is_gimbal_type = False
        self.detection_results = None  # 新增：检测结果，用于绘制检测框


class TargetPriorityManager:
    """目标优先级管理器，处理目标连续性和优先级逻辑"""

    def __init__(self):
        self.last_main_target = None
        self.demo_missing_count = 0
        self.max_demo_missing_frames = 2  # 允许demo目标最多丢失的帧数

    def update_target_priority(self, demo_boxes, kk_boxes):
        """
        更新目标优先级，处理目标连续性

        参数：
        - demo_boxes: 检测到的demo目标框列表
        - kk_boxes: 检测到的kk目标框列表

        返回：
        - (demo_boxes, kk_boxes, current_main_target): 处理后的目标框和当前主目标
        """
        # 如果同时有kk和demo，只保留优先级高的目标，这里设定demo优先
        if demo_boxes and kk_boxes:
            kk_boxes = []  # 清空kk_boxes，优先处理demo
            self.last_main_target = "demo"
            self.demo_missing_count = 0
            return demo_boxes, kk_boxes, "demo"

        elif demo_boxes:
            self.last_main_target = "demo"
            self.demo_missing_count = 0
            return demo_boxes, kk_boxes, "demo"

        elif kk_boxes:
            # 仅在上一帧是demo且本帧短暂丢失demo时，允许延续demo优先
            if self.last_main_target == "demo":
                self.demo_missing_count += 1
                if self.demo_missing_count <= self.max_demo_missing_frames:
                    # 保持上一帧主目标为demo，但不把kk当作demo
                    # 这里返回空的demo_boxes，表示暂时忽略kk目标，等待demo重新出现
                    return [], [], "demo_missing"
                else:
                    # 超过最大丢失帧数，切换到kk目标
                    self.last_main_target = "kk"
                    self.demo_missing_count = 0
                    return demo_boxes, kk_boxes, "kk"
            else:
                self.last_main_target = "kk"
                self.demo_missing_count = 0
                return demo_boxes, kk_boxes, "kk"

        else:
            # 没有任何目标
            self.last_main_target = None
            self.demo_missing_count = 0
            return demo_boxes, kk_boxes, None


# 全局目标优先级管理器实例
_target_priority_manager = TargetPriorityManager()


def reset_gimbal_align_state():
    """重置云台对准算法的状态，用于新任务开始时"""
    global _target_priority_manager
    _target_priority_manager = TargetPriorityManager()


def get_gimbal_align_state():
    """获取当前云台对准算法的状态信息，用于调试"""
    global _target_priority_manager
    return {
        'last_main_target': _target_priority_manager.last_main_target,
        'demo_missing_count': _target_priority_manager.demo_missing_count,
        'max_demo_missing_frames': _target_priority_manager.max_demo_missing_frames
    }


def get_3x3_grid_position(cx, cy, width, height):
    """根据坐标(cx,cy)判断落在哪个3x3格子里"""
    col = 0
    row = 0
    third_w = width / 3
    third_h = height / 3

    if cx < third_w:
        col = 0  # 左
    elif cx < 2 * third_w:
        col = 1  # 中
    else:
        col = 2  # 右

    if cy < third_h:
        row = 0  # 上
    elif cy < 2 * third_h:
        row = 1  # 中
    else:
        row = 2  # 下

    grid_map = {
        (0, 0): 'LT',
        (1, 0): 'T',
        (2, 0): 'RT',
        (0, 1): 'L',
        (1, 1): 'C',
        (2, 1): 'R',
        (0, 2): 'LB',
        (1, 2): 'B',
        (2, 2): 'RB'
    }
    return grid_map[(col, row)]


def calculate_yaw_pitch_adjust(cx, cy, width, height):
    """计算云台需要调整的yaw和pitch角度（度），基于3x3九宫格判断"""
    grid_pos = get_3x3_grid_position(cx, cy, width, height)

    pitch_adjust = 0.0
    yaw_adjust = 0.0

    # 九宫格上方格子 -> pitch上调(+)
    if grid_pos in ['LT', 'T', 'RT']:
        pitch_adjust = MAX_ADJUST_DEGREE
    # 九宫格下方格子 -> pitch下调(-)
    if grid_pos in ['LB', 'B', 'RB']:
        pitch_adjust = -MAX_ADJUST_DEGREE
    # 九宫格左方格子 -> yaw左转(+)
    if grid_pos in ['LT', 'L', 'LB']:
        yaw_adjust = MAX_ADJUST_DEGREE
    # 九宫格右方格子 -> yaw右转(-)
    if grid_pos in ['RT', 'R', 'RB']:
        yaw_adjust = -MAX_ADJUST_DEGREE

    return yaw_adjust, pitch_adjust, grid_pos


def calculate_demo_target_adjust(cx, cy, bbox_area, frame_width, frame_height):
    """demo目标的云台调整和焦距调整逻辑"""

    # 画面中心点
    center_x = frame_width / 2
    center_y = frame_height / 2

    # 中心偏差百分比阈值(5%)
    max_offset_ratio = 0.15

    offset_x = (cx - center_x) / frame_width
    offset_y = (cy - center_y) / frame_height

    pitch_adjust = 0.0
    yaw_adjust = 0.0
    status_msgs = []

    # 先调整yaw/pitch让中心点靠近画面中心
    if abs(offset_x) > max_offset_ratio:
        # x偏左 画面需要云台向左转 (+yaw)，否则向右转 (-yaw)
        yaw_adjust = MAX_ADJUST_DEGREE if offset_x < 0 else -MAX_ADJUST_DEGREE
        status_msgs.append(f"云台yaw调整 {'左转' if yaw_adjust > 0 else '右转'} {abs(yaw_adjust):.1f}°")
    if abs(offset_y) > max_offset_ratio:
        # y偏上 画面需要云台向上调 (+pitch)，否则向下调 (-pitch)
        pitch_adjust = MAX_ADJUST_DEGREE if offset_y < 0 else -MAX_ADJUST_DEGREE
        status_msgs.append(f"云台pitch调整 {'上调' if pitch_adjust > 0 else '下调'} {abs(pitch_adjust):.1f}°")

    # bbox面积比例阈值判断，画面大小面积
    frame_area = frame_width * frame_height
    # min_area_ratio = 0.5 / 3  # 约1/6, 你可以调
    min_area_ratio = 0.3
    max_area_ratio = 2 / 3

    # 判断是否要调整焦距
    if bbox_area < min_area_ratio * frame_area:
        status_msgs.append("拉近焦距")
    elif bbox_area > max_area_ratio * frame_area:
        status_msgs.append("拉远焦距")
    else:
        # 中心偏差都很小且bbox面积合适，执行拍照
        if abs(offset_x) <= max_offset_ratio and abs(offset_y) <= max_offset_ratio:
            status_msgs.append("拍照")
            status_msgs.append("恢复焦距")

    if not status_msgs:
        status_msgs.append("等待调整")

    return yaw_adjust, pitch_adjust, status_msgs


def adjust_gimbal_for_kk(box, width, height):
    """
    针对 'kk' 目标（如关键构件）进行云台粗略调整。

    参数说明：
    - box: 检测到的目标框，含xyxy格式的坐标。
    - width: 当前图像的宽度。
    - height: 当前图像的高度。

    返回：
    - pitch_adjust: 云台俯仰角调整值（正数上抬，负数下压）。
    - yaw_adjust: 云台偏航角调整值（正数向右，负数向左）。
    - msg: 当前动作的文本说明。
    """
    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
    w, h = x2 - x1, y2 - y1
    cx, cy = x1 + w / 2, y1 + h / 2
    grid_w, grid_h = width / 3, height / 3
    col, row = int(cx // grid_w), int(cy // grid_h)

    grid_names = [["LT", "T", "RT"], ["L", "C", "R"], ["LB", "B", "RB"]]
    grid_pos = grid_names[row][col]

    adj_deg = 3.0
    pitch_adjust = 0.0
    yaw_adjust = 0.0

    if grid_pos in ["LT", "T", "RT"]:
        pitch_adjust += adj_deg
    elif grid_pos in ["LB", "B", "RB"]:
        pitch_adjust -= adj_deg
    if grid_pos in ["LT", "L", "LB"]:
        yaw_adjust -= adj_deg
    elif grid_pos in ["RT", "R", "RB"]:
        yaw_adjust += adj_deg

    msg = (f"kk目标检测，格子位置: {grid_pos}，云台动作: pitch {pitch_adjust:+.1f}°, yaw {yaw_adjust:+.1f}°, "
           f"目标中心像素:({cx:.1f}, {cy:.1f})")
    return pitch_adjust, yaw_adjust, msg


def adjust_gimbal_for_kk_and_zoom(box, width, height):
    """
    针对 'kk' 目标进行云台粗略调整 + 焦距判断（不拍照）。

    返回：
    - pitch_adjust: 云台俯仰角调整值
    - yaw_adjust: 云台偏航角调整值
    - zoom_msg: 焦距操作说明
    - msg: 综合说明（含焦距建议）
    """
    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
    w, h = x2 - x1, y2 - y1
    cx, cy = x1 + w / 2, y1 + h / 2

    # 九宫格位置判断
    grid_w, grid_h = width / 3, height / 3
    col, row = int(cx // grid_w), int(cy // grid_h)
    grid_names = [["LT", "T", "RT"], ["L", "C", "R"], ["LB", "B", "RB"]]
    grid_pos = grid_names[row][col]

    # 判断是否中心对准
    dx = cx - width / 2
    dy = cy - height / 2
    dx_ratio = dx / width
    dy_ratio = dy / height
    is_centered = abs(dx_ratio) <= 0.2 and abs(dy_ratio) <= 0.14

    # 云台初始调整
    adj_deg = 3.0
    yaw_deg = 8.0
    pitch_adjust = 0.0
    yaw_adjust = 0.0

    if grid_pos != "C":
        # 按九宫格粗略调
        if grid_pos in ["LT", "T", "RT"]:
            pitch_adjust += adj_deg
        elif grid_pos in ["LB", "B", "RB"]:
            pitch_adjust -= adj_deg
        if grid_pos in ["LT", "L", "LB"]:
            yaw_adjust -= yaw_deg
        elif grid_pos in ["RT", "R", "RB"]:
            yaw_adjust += yaw_deg
    else:
        # 中心格，按 dx/dy 调
        fine_adj = 2.0
        if dx_ratio > 0.07:
            yaw_adjust = -fine_adj
        elif dx_ratio < -0.07:
            yaw_adjust = fine_adj
        if dy_ratio > 0.07:
            pitch_adjust = -fine_adj
        elif dy_ratio < -0.07:
            pitch_adjust = fine_adj

    # 面积占比判断
    area_ratio = (w * h) / (width * height)

    # 焦距调整逻辑
    zoom_msg = ""
    zoom_adjust_pitch = 0.0
    zoom_adjust_yaw = 0.0

    if is_centered:
        if area_ratio > 0.2:
            zoom_msg = "拉远焦距"
    else:
        if area_ratio > 0.2:
            zoom_msg = "拉远焦距"
            # 进一步微调云台
            if dx_ratio > 0.1:
                zoom_adjust_yaw = -3.0
            elif dx_ratio < -0.07:
                zoom_adjust_yaw = 3.0
            if dy_ratio > 0.07:
                zoom_adjust_pitch = -2.0
            elif dy_ratio < -0.07:
                zoom_adjust_pitch = 2.0
        elif area_ratio < 0.2:
            pass
        else:
            zoom_msg = "中心未对准，焦距暂不调整"

    pitch_adjust += zoom_adjust_pitch
    yaw_adjust += zoom_adjust_yaw

    msg = (
        f"kk目标，格子:{grid_pos}，pitch {pitch_adjust:+.1f}°, yaw {yaw_adjust:+.1f}°，"
        f"中心偏差(dx: {dx_ratio:.3f}, dy: {dy_ratio:.3f})，面积占比:{area_ratio:.2f}，"
        f"焦距建议: {zoom_msg}"
    )

    return pitch_adjust, yaw_adjust, zoom_msg, msg


def adjust_gimbal_for_demo(box, width, height, cx_center, cy_center):
    """
    针对 demo 目标进行精确调整与拍照判断（含变焦时角度微调）。
    """
    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
    w, h = x2 - x1, y2 - y1
    cx, cy = x1 + w / 2, y1 + h / 2

    dx, dy = cx - cx_center, cy - cy_center
    dx_ratio, dy_ratio = dx / width, dy / height
    threshold_ratio = 0.05

    bbox_area = w * h
    area_ratio = bbox_area / (width * height)

    # 是否中心对准 & 面积合适，满足拍照条件
    if abs(dx_ratio) <= threshold_ratio and abs(dy_ratio) <= threshold_ratio and 0.24 <= area_ratio <= 0.66:
        msg = (f"demo目标中心接近画面中心，bbox面积占比{area_ratio:.2f}，执行拍照。")
        return 0.0, 0.0, "", msg, True

    # 否则：变焦 + 云台偏移控制
    zoom_msg = "拉近焦距" if area_ratio < 0.24 else "拉远焦距" if area_ratio > 0.66 else "焦距合适，准备拍照"

    # 云台调整角度
    base_adj_deg = 2.0
    pitch_adjust, yaw_adjust = 0.0, 0.0

    if abs(dx_ratio) > threshold_ratio:
        yaw_adjust = base_adj_deg if dx > 0 else -base_adj_deg  # dx > 0 目标在右，右转
    if abs(dy_ratio) > threshold_ratio:
        pitch_adjust = -base_adj_deg if dy > 0 else base_adj_deg  # dy > 0 目标在下，下压

    # 如果焦距需要调整，则额外再加上补偿角度
    if zoom_msg in ["拉近焦距", "拉远焦距"]:
        zoom_adj_deg = 1.0  # 变焦时额外微调角度

        if dx_ratio > 0.1:
            yaw_adjust += zoom_adj_deg  # 向右更偏，右转更多
        elif dx_ratio < -0.05:
            yaw_adjust -= zoom_adj_deg  # 向左更偏，左转更多

        if dy_ratio > 0.1:
            pitch_adjust -= zoom_adj_deg  # 目标更下，下压更多
        elif dy_ratio < -0.05:
            pitch_adjust += zoom_adj_deg  # 目标更上，上抬更多

    msg = (
        f"demo目标调整中: 中心偏差(dx: {dx_ratio:.3f}, dy: {dy_ratio:.3f}), "
        f"面积占比: {area_ratio:.2f}, 焦距操作: {zoom_msg}"
    )
    return pitch_adjust, yaw_adjust, zoom_msg, msg, False


def process_gimbal_alignment(frame, frame_width, frame_height, frame_count,
                             target_classes):
    """
    主要的云台对准算法处理函数

    参数：
    - frame: 输入图像帧
    - frame_width: 帧宽度
    - frame_height: 帧高度
    - frame_count: 帧计数
    - target_classes: 目标类别列表

    返回：
    - GimbalAlignResult: 包含所有控制参数的结果对象
    """
    global _target_priority_manager, gimbal_align_model
    result = GimbalAlignResult()

    # 进行目标检测
    detection_results = gimbal_align_model(frame)

    # 将检测结果保存到结果对象中，供业务代码绘制检测框使用
    result.detection_results = detection_results

    # 设定画面中心点
    cx_center = frame_width / 2
    cy_center = frame_height / 2

    # 无目标时的处理
    if len(detection_results[0].boxes) == 0:
        # 更新目标优先级管理器状态
        _target_priority_manager.update_target_priority([], [])

        result.status_msg = f"无目标，云台向上调整 pitch +{PITCH_STEP_NO_TARGET} 度"
        # 新增：限制云台向上调整的次数，避免不断升高
        if frame_count % 20 == 0:  # 每20帧才允许调整一次
            result.pitch_adjust = PITCH_STEP_NO_TARGET
        else:
            result.pitch_adjust = 0.0
        result.yaw_adjust = 0.0
        result.is_gimbal_adjust = True
        # 切换云台模式
        result.gimbal_type_msg = "广角"
        result.is_gimbal_type = True
        return result

    # 有目标时的处理
    result.gimbal_type_msg = "变焦"
    result.is_gimbal_type = True

    # 分类收集目标框
    kk_boxes = []
    demo_boxes = []

    for box in detection_results[0].boxes:
        cls_id = int(box.cls[0])
        cls_name = detection_results[0].names[cls_id].strip().lower()

        # 强制跳过非目标类别
        if cls_name not in target_classes:
            continue

        confidence = float(box.conf[0])
        # 根据类别获取对应的置信度阈值
        threshold = GIMBAL_ALIGN_CONFIDENCE_THRESHOLDS.get(cls_name, 0.1)

        if confidence < threshold:
            continue

        if cls_name == "kk":
            kk_boxes.append(box)
        elif cls_name == "demo":
            demo_boxes.append(box)

    # 使用目标优先级管理器处理目标优先级和连续性
    demo_boxes, kk_boxes, current_main_target = _target_priority_manager.update_target_priority(demo_boxes, kk_boxes)

    # 根据目标优先级管理器的结果进行处理
    if current_main_target == "demo_missing":
        # demo目标短暂丢失，等待重新出现
        result.status_msg = f"demo目标短暂丢失(第{_target_priority_manager.demo_missing_count}帧)，等待重新出现"
        result.pitch_adjust = 0.0
        result.yaw_adjust = 0.0
        result.is_gimbal_adjust = False
        return result

    # 处理demo目标
    if demo_boxes:
        pitch_adjust, yaw_adjust, zoom_msg, status_msg, should_capture = adjust_gimbal_for_demo(
            demo_boxes[0], frame_width, frame_height, cx_center, cy_center)

        result.pitch_adjust = pitch_adjust
        result.yaw_adjust = yaw_adjust
        result.zoom_msg = zoom_msg
        result.status_msg = status_msg

        if should_capture:
            result.is_take_photo = True
        else:
            # 新增：只有中心对准时才允许拉近焦距，否则优先调整云台
            center_threshold = 0.05
            x1, y1, x2, y2 = demo_boxes[0].xyxy[0].cpu().numpy().astype(int)
            w, h = x2 - x1, y2 - y1
            cx, cy = x1 + w / 2, y1 + h / 2
            dx, dy = cx - cx_center, cy - cy_center
            dx_ratio, dy_ratio = dx / frame_width, dy / frame_height
            center_ok = abs(dx_ratio) <= center_threshold and abs(dy_ratio) <= center_threshold

            if not center_ok:
                # 只调整云台，不拉焦距
                if pitch_adjust != 0 or yaw_adjust != 0:
                    result.is_gimbal_adjust = True
                result.zoom_msg = ""
                result.is_zoom = False
            else:
                # 中心对准后才允许拉近/拉远焦距
                if zoom_msg in ["拉近焦距", "拉远焦距"]:
                    result.is_zoom = True
                if pitch_adjust != 0 or yaw_adjust != 0:
                    result.is_gimbal_adjust = True

    # 处理kk目标
    elif kk_boxes:
        pitch_adjust, yaw_adjust, zoom_msg, status_msg = adjust_gimbal_for_kk_and_zoom(
            kk_boxes[0], frame_width, frame_height)

        result.pitch_adjust = pitch_adjust
        result.yaw_adjust = yaw_adjust
        result.zoom_msg = zoom_msg
        result.status_msg = status_msg
        result.is_gimbal_adjust = True

        if zoom_msg == "拉远焦距":
            result.is_zoom = True

    else:
        # 画面有目标但非目标类别
        result.status_msg = "检测到非目标类别，无云台动作"

    return result

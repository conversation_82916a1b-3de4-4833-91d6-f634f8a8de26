import os
import shutil

from config.config import logger
from service.auto_drone_control import drone_control_predict, update_gimbal_params, update_gimbal_control, \
    get_drone_control_exit_event
from service.image_process import image_process
from service.live_stream_process import process_stream, get_exit_event
from service.oss import oss_client
from service.video_process import parse_opt, video_predict
from utils import get_filename_from_url, download_file, make_msg_resp


# 开启实时控制连接请求
def start_real_time_control_req(ws_client, data):
    service_type = "real_time_control_resp"

    resp_data = {
        "job_id": data.get("job_id"),
        "drone_sn": data.get("drone_sn"),
        "dock_sn": data.get("dock_sn"),
    }

    # 开启处理
    err = drone_control_predict(ws_client, data, service_type)
    if ws_handle_error(ws_client, resp_data, err, service_type):
        return


# 关闭实时控制连接请求
def end_real_time_control_req(ws_client, data):
    exit_evt = get_drone_control_exit_event()
    if exit_evt is not None:
        exit_evt.set()


# 实时控制请求，请求算法做控制决策
def real_time_control_req(ws_client, data):
    initial_distance = data.get("initial_distance")

    # 1.调用update_gimbal_params初始化参数
    update_gimbal_params(initial_distance)

    # 2.调用update_gimbal_control，开启gimbal_control_enabled，
    update_gimbal_control(True)


def ws_handle_error(ws_client, data, err, service_type):
    if err is not None:
        logger.error(err)
        ws_client.send_message(make_msg_resp(service_type, data=data, succeeded=False, msg=err))
        return True
    return False

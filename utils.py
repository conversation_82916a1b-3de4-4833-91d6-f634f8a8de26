import os
from urllib.parse import urlparse, unquote

import cv2
import numpy as np
import requests
from PIL import Image, ImageDraw, ImageFont

from config.config import config_data

# 全局字体缓存，避免重复加载字体文件
_font_cache = {}


def get_font(font_size=20):
    """获取缓存的字体对象，避免重复加载"""
    if font_size not in _font_cache:
        font_path = os.path.join(".", "static", "font", "SimHei.ttf")
        _font_cache[font_size] = ImageFont.truetype(font_path, font_size)
    return _font_cache[font_size]


def draw_chinese_text(image, text, position, font_size=20, color=(0, 255, 0)):
    """优化后的中文文本绘制函数，使用缓存字体"""
    pil_img = Image.fromarray(image)
    draw = ImageDraw.Draw(pil_img)
    font = get_font(font_size)
    draw.text(position, text, font=font, fill=color)
    return np.array(pil_img)


def draw_detection_boxes_batch(frame, filtered_boxes, results, class_name_mapping):
    """批量绘制检测框，优化性能"""
    if not filtered_boxes:
        return frame

    # 一次性转换为PIL图像
    pil_img = Image.fromarray(frame)
    draw = ImageDraw.Draw(pil_img)
    font = get_font(20)  # 使用缓存的字体

    # 批量绘制所有检测框和文本
    for box in filtered_boxes:
        cls_name = results[0].names[int(box.cls[0])]
        chinese_name, color = class_name_mapping.get(cls_name, (cls_name, (0, 255, 0)))
        xyxy = box.xyxy[0].cpu().numpy().astype(int)
        confidence = box.conf[0]

        # 绘制矩形框（PIL格式）
        draw.rectangle([xyxy[0], xyxy[1], xyxy[2], xyxy[3]], outline=color, width=3)

        # 绘制文本
        text = f"{chinese_name} {confidence:.2f}"
        text_position = (xyxy[0], max(0, xyxy[1] - 25))  # 确保文本不会超出图像边界
        draw.text(text_position, text, font=font, fill=color)

    # 一次性转换回OpenCV格式
    return np.array(pil_img)


def draw_fps_time(image, fps, current_time, frame_count):
    cv2.putText(image, f"Frame: {frame_count}", (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2, cv2.LINE_AA)
    cv2.putText(image, f"FPS: {fps:.2f}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2, cv2.LINE_AA)
    cv2.putText(image, f"Time: {current_time}", (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2, cv2.LINE_AA)
    return image


# 文件批量上传
def upload_file(file_path_list, file_id):
    url = config_data.get("server", {}).get("upload_file_url")

    files = [("file", (open(file_path, "rb"))) for file_path in file_path_list]
    data = {
        "file_id": file_id,
        "result_type": "alarm_video"
    }

    response = requests.post(url, files=files, data=data)

    if response.status_code == 200:
        try:
            result = response.json()
            return result, None
        except Exception as e:
            err = f"failed to upload file, or parse json err: {e}"
            return None, err
    else:
        err = f"failed to upload file，status code: {response.status_code}, response: {response.text}"
        return None, err


# 文件下载
def download_file(url, save_path):
    # 确保保存文件的目录存在
    os.makedirs(os.path.dirname(save_path), exist_ok=True)

    response = requests.get(url, stream=True)
    if response.status_code == 200:
        with open(save_path, 'wb') as f:
            f.write(response.content)
        return None
    else:
        err = f"failed to download file: {response.status_code}"
        return err


# 根据url获取文件名
def get_filename_from_url(url):
    # 解析 URL 获取路径部分
    parsed_url = urlparse(url)
    # 对路径进行解码，避免编码字符
    path = unquote(parsed_url.path)
    # 获取文件名
    filename = os.path.basename(path)
    return filename


def make_msg_resp(service_type, data, succeeded=True, msg=""):
    return {
        "type": service_type,
        "succeeded": succeeded,
        "msg": msg,
        "data": data
    }


def is_frame_corrupted(frame):
    """检查帧是否损坏或异常
    Args:
        frame: 输入帧
    Returns:
        bool: True表示帧损坏，False表示帧正常
    """
    if frame is None:
        return True

    # 检查帧的基本属性
    if frame.size == 0:
        return True

    # 检查帧的形状
    if len(frame.shape) != 3:
        return True

    height, width, channels = frame.shape
    if height <= 0 or width <= 0 or channels not in [1, 3, 4]:
        return True

    # 检查是否全为同一颜色（可能是绿屏、黑屏等）
    if channels == 3:
        # 计算每个通道的标准差
        std_b = np.std(frame[:, :, 0])
        std_g = np.std(frame[:, :, 1])
        std_r = np.std(frame[:, :, 2])

        # 如果所有通道的标准差都很小，可能是纯色帧
        if std_b < 5 and std_g < 5 and std_r < 5:
            # 检查是否是绿色帧（绿条问题）
            mean_b = np.mean(frame[:, :, 0])
            mean_g = np.mean(frame[:, :, 1])
            mean_r = np.mean(frame[:, :, 2])

            # 如果绿色通道明显高于其他通道，可能是绿屏
            if mean_g > mean_b + 50 and mean_g > mean_r + 50:
                return True

            # 如果是全黑帧也跳过
            if mean_b < 10 and mean_g < 10 and mean_r < 10:
                return True

    # 检查数据类型
    if frame.dtype != np.uint8:
        return True

    # 检查数据范围
    if np.any(frame < 0) or np.any(frame > 255):
        return True

    return False


def detect_green_artifact(frame):
    """专门检测绿条/绿屏伪影
    Args:
        frame: 输入帧
    Returns:
        bool: True表示检测到绿色伪影，False表示正常
    """
    if frame is None or frame.size == 0:
        return True

    try:
        height, width = frame.shape[:2]

        # 检测水平绿条
        for y in range(0, height, max(1, height // 20)):  # 采样检测
            row = frame[y, :, :]
            green_ratio = np.sum((row[:, 1] > row[:, 0] + 30) & (row[:, 1] > row[:, 2] + 30)) / width
            if green_ratio > 0.7:  # 如果一行中70%的像素都是绿色主导
                return True

        # 检测垂直绿条
        for x in range(0, width, max(1, width // 20)):  # 采样检测
            col = frame[:, x, :]
            green_ratio = np.sum((col[:, 1] > col[:, 0] + 30) & (col[:, 1] > col[:, 2] + 30)) / height
            if green_ratio > 0.7:  # 如果一列中70%的像素都是绿色主导
                return True

        # 检测区块绿屏
        block_size = min(64, height // 4, width // 4)
        if block_size > 0:
            for y in range(0, height - block_size, block_size):
                for x in range(0, width - block_size, block_size):
                    block = frame[y:y + block_size, x:x + block_size, :]
                    mean_g = np.mean(block[:, :, 1])
                    mean_b = np.mean(block[:, :, 0])
                    mean_r = np.mean(block[:, :, 2])

                    # 检查是否为绿色块
                    if mean_g > mean_b + 40 and mean_g > mean_r + 40 and mean_g > 100:
                        std_block = np.std(block)
                        if std_block < 15:  # 变化很小的绿色块
                            return True

        return False

    except Exception as e:
        # 绿色伪影检测失败时返回False，不影响正常处理
        return False


def validate_frame_integrity(frame):
    """验证帧的完整性
    Args:
        frame: 输入帧
    Returns:
        bool: True表示帧完整，False表示帧有问题
    """
    try:
        # if is_frame_corrupted(frame):
        #     return False

        # 专门检测绿色伪影
        # fixme 暂时关闭
        # if detect_green_artifact(frame):
        #     return False

        # 尝试进行简单的图像操作来验证帧的有效性
        # test_resize = cv2.resize(frame, (frame.shape[1]//2, frame.shape[0]//2))
        # if test_resize is None or test_resize.size == 0:
        #     return False

        return True
    except Exception as e:
        # 帧完整性验证失败时返回False，不影响正常处理
        return False

#!/usr/bin/env python3
"""
测试目标优先级管理逻辑
验证 last_main_target 相关功能是否正常工作
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from service.gimbal_align import TargetPriorityManager, reset_gimbal_align_state, get_gimbal_align_state


def test_target_priority_manager():
    """测试目标优先级管理器"""
    print("=== 测试目标优先级管理器 ===")
    
    # 创建管理器实例
    manager = TargetPriorityManager()
    
    # 模拟目标框（简化表示）
    demo_box = ["demo_box"]
    kk_box = ["kk_box"]
    
    print("\n1. 测试同时检测到demo和kk目标")
    demo_boxes, kk_boxes, target = manager.update_target_priority([demo_box], [kk_box])
    print(f"输入: demo=[demo_box], kk=[kk_box]")
    print(f"输出: demo={demo_boxes}, kk={kk_boxes}, target={target}")
    print(f"状态: last_main_target={manager.last_main_target}, demo_missing_count={manager.demo_missing_count}")
    assert target == "demo", "demo应该优先"
    assert len(kk_boxes) == 0, "kk_boxes应该被清空"
    
    print("\n2. 测试只有demo目标")
    demo_boxes, kk_boxes, target = manager.update_target_priority([demo_box], [])
    print(f"输入: demo=[demo_box], kk=[]")
    print(f"输出: demo={demo_boxes}, kk={kk_boxes}, target={target}")
    print(f"状态: last_main_target={manager.last_main_target}, demo_missing_count={manager.demo_missing_count}")
    assert target == "demo", "应该是demo目标"
    
    print("\n3. 测试demo目标丢失第1帧")
    demo_boxes, kk_boxes, target = manager.update_target_priority([], [kk_box])
    print(f"输入: demo=[], kk=[kk_box]")
    print(f"输出: demo={demo_boxes}, kk={kk_boxes}, target={target}")
    print(f"状态: last_main_target={manager.last_main_target}, demo_missing_count={manager.demo_missing_count}")
    assert target == "demo_missing", "应该等待demo重新出现"
    assert manager.demo_missing_count == 1, "丢失计数应该是1"
    
    print("\n4. 测试demo目标丢失第2帧")
    demo_boxes, kk_boxes, target = manager.update_target_priority([], [kk_box])
    print(f"输入: demo=[], kk=[kk_box]")
    print(f"输出: demo={demo_boxes}, kk={kk_boxes}, target={target}")
    print(f"状态: last_main_target={manager.last_main_target}, demo_missing_count={manager.demo_missing_count}")
    assert target == "demo_missing", "应该等待demo重新出现"
    assert manager.demo_missing_count == 2, "丢失计数应该是2"
    
    print("\n5. 测试demo目标丢失第3帧（超过阈值）")
    demo_boxes, kk_boxes, target = manager.update_target_priority([], [kk_box])
    print(f"输入: demo=[], kk=[kk_box]")
    print(f"输出: demo={demo_boxes}, kk={kk_boxes}, target={target}")
    print(f"状态: last_main_target={manager.last_main_target}, demo_missing_count={manager.demo_missing_count}")
    assert target == "kk", "应该切换到kk目标"
    assert manager.last_main_target == "kk", "主目标应该切换到kk"
    assert manager.demo_missing_count == 0, "丢失计数应该重置"
    
    print("\n6. 测试demo重新出现")
    demo_boxes, kk_boxes, target = manager.update_target_priority([demo_box], [kk_box])
    print(f"输入: demo=[demo_box], kk=[kk_box]")
    print(f"输出: demo={demo_boxes}, kk={kk_boxes}, target={target}")
    print(f"状态: last_main_target={manager.last_main_target}, demo_missing_count={manager.demo_missing_count}")
    assert target == "demo", "应该立即切换回demo"
    assert manager.last_main_target == "demo", "主目标应该是demo"
    assert manager.demo_missing_count == 0, "丢失计数应该重置"
    
    print("\n✅ 目标优先级管理器测试通过！")


def test_global_state_functions():
    """测试全局状态管理函数"""
    print("\n=== 测试全局状态管理函数 ===")
    
    # 重置状态
    reset_gimbal_align_state()
    state = get_gimbal_align_state()
    print(f"重置后状态: {state}")
    assert state['last_main_target'] is None, "重置后主目标应该为None"
    assert state['demo_missing_count'] == 0, "重置后丢失计数应该为0"
    
    print("✅ 全局状态管理函数测试通过！")


def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    manager = TargetPriorityManager()
    
    print("\n1. 测试没有任何目标")
    demo_boxes, kk_boxes, target = manager.update_target_priority([], [])
    print(f"输入: demo=[], kk=[]")
    print(f"输出: demo={demo_boxes}, kk={kk_boxes}, target={target}")
    print(f"状态: last_main_target={manager.last_main_target}, demo_missing_count={manager.demo_missing_count}")
    assert target is None, "没有目标时应该返回None"
    
    print("\n2. 测试只有kk目标（没有历史demo）")
    demo_boxes, kk_boxes, target = manager.update_target_priority([], ["kk_box"])
    print(f"输入: demo=[], kk=[kk_box]")
    print(f"输出: demo={demo_boxes}, kk={kk_boxes}, target={target}")
    print(f"状态: last_main_target={manager.last_main_target}, demo_missing_count={manager.demo_missing_count}")
    assert target == "kk", "应该处理kk目标"
    
    print("\n✅ 边界情况测试通过！")


if __name__ == "__main__":
    try:
        test_target_priority_manager()
        test_global_state_functions()
        test_edge_cases()
        print("\n🎉 所有测试通过！目标优先级管理逻辑工作正常。")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

#!/usr/bin/env python3
"""
测试常量配置
验证所有常量是否正确导入和配置
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from service.gimbal_align import CONFIDENCE_THRESHOLDS, CLASS_NAME_MAPPING


def test_confidence_thresholds():
    """测试置信度阈值配置"""
    print("=== 测试置信度阈值配置 ===")
    
    print(f"CONFIDENCE_THRESHOLDS: {CONFIDENCE_THRESHOLDS}")
    
    # 验证配置存在
    assert 'demo' in CONFIDENCE_THRESHOLDS, "demo阈值应该存在"
    assert 'kk' in CONFIDENCE_THRESHOLDS, "kk阈值应该存在"
    assert CONFIDENCE_THRESHOLDS['demo'] == 0.1, "demo阈值应该是0.1"
    assert CONFIDENCE_THRESHOLDS['kk'] == 0.1, "kk阈值应该是0.1"
    
    print("✅ 置信度阈值配置正确！")


def test_class_name_mapping():
    """测试类别名称映射配置"""
    print("\n=== 测试类别名称映射配置 ===")
    
    print(f"CLASS_NAME_MAPPING: {CLASS_NAME_MAPPING}")
    
    # 验证配置存在
    assert 'demo' in CLASS_NAME_MAPPING, "demo映射应该存在"
    assert 'kk' in CLASS_NAME_MAPPING, "kk映射应该存在"
    
    # 验证映射格式
    demo_mapping = CLASS_NAME_MAPPING['demo']
    kk_mapping = CLASS_NAME_MAPPING['kk']
    
    assert isinstance(demo_mapping, tuple), "demo映射应该是元组"
    assert len(demo_mapping) == 2, "demo映射应该有2个元素"
    assert isinstance(demo_mapping[0], str), "demo映射第一个元素应该是字符串"
    assert isinstance(demo_mapping[1], tuple), "demo映射第二个元素应该是颜色元组"
    assert len(demo_mapping[1]) == 3, "颜色元组应该有3个元素(RGB)"
    
    assert isinstance(kk_mapping, tuple), "kk映射应该是元组"
    assert len(kk_mapping) == 2, "kk映射应该有2个元素"
    assert isinstance(kk_mapping[0], str), "kk映射第一个元素应该是字符串"
    assert isinstance(kk_mapping[1], tuple), "kk映射第二个元素应该是颜色元组"
    assert len(kk_mapping[1]) == 3, "颜色元组应该有3个元素(RGB)"
    
    # 验证具体值
    assert demo_mapping[0] == '桥墩', "demo应该映射到'桥墩'"
    assert demo_mapping[1] == (0, 155, 0), "demo颜色应该是绿色"
    assert kk_mapping[0] == '不完整桥墩', "kk应该映射到'不完整桥墩'"
    assert kk_mapping[1] == (0, 0, 155), "kk颜色应该是蓝色"
    
    print("✅ 类别名称映射配置正确！")


def test_constants_import():
    """测试常量导入"""
    print("\n=== 测试常量导入 ===")
    
    try:
        from service.bridge_inspection import CONFIDENCE_THRESHOLDS as BRIDGE_CONFIDENCE_THRESHOLDS
        from service.bridge_inspection import CLASS_NAME_MAPPING as BRIDGE_CLASS_NAME_MAPPING
        
        # 验证导入的常量与原始常量一致
        assert BRIDGE_CONFIDENCE_THRESHOLDS == CONFIDENCE_THRESHOLDS, "bridge_inspection中的CONFIDENCE_THRESHOLDS应该与gimbal_align一致"
        assert BRIDGE_CLASS_NAME_MAPPING == CLASS_NAME_MAPPING, "bridge_inspection中的CLASS_NAME_MAPPING应该与gimbal_align一致"
        
        print("✅ 常量导入正确！")
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        raise


def test_get_mapping_logic():
    """测试映射获取逻辑"""
    print("\n=== 测试映射获取逻辑 ===")
    
    # 测试已知类别
    demo_name, demo_color = CLASS_NAME_MAPPING.get('demo', ('demo', (0, 255, 0)))
    kk_name, kk_color = CLASS_NAME_MAPPING.get('kk', ('kk', (0, 255, 0)))
    
    print(f"demo映射: {demo_name}, {demo_color}")
    print(f"kk映射: {kk_name}, {kk_color}")
    
    assert demo_name == '桥墩', "demo名称映射正确"
    assert demo_color == (0, 155, 0), "demo颜色映射正确"
    assert kk_name == '不完整桥墩', "kk名称映射正确"
    assert kk_color == (0, 0, 155), "kk颜色映射正确"
    
    # 测试未知类别（使用默认值）
    unknown_name, unknown_color = CLASS_NAME_MAPPING.get('unknown', ('unknown', (0, 255, 0)))
    print(f"未知类别映射: {unknown_name}, {unknown_color}")
    assert unknown_name == 'unknown', "未知类别应该使用默认名称"
    assert unknown_color == (0, 255, 0), "未知类别应该使用默认颜色"
    
    print("✅ 映射获取逻辑正确！")


if __name__ == "__main__":
    try:
        test_confidence_thresholds()
        test_class_name_mapping()
        test_constants_import()
        test_get_mapping_logic()
        print("\n🎉 所有常量配置测试通过！")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
